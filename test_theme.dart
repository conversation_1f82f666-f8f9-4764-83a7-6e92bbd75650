import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/src/config/theme_provider.dart';
import 'lib/src/config/app_themes.dart';
import 'lib/src/config/theme_helper.dart';

void main() {
  runApp(const ThemeTestApp());
}

class ThemeTestApp extends StatelessWidget {
  const ThemeTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ThemeProvider(),
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Theme Test',
            theme: AppThemes.lightTheme,
            darkTheme: AppThemes.darkTheme,
            themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            home: const ThemeTestScreen(),
          );
        },
      ),
    );
  }
}

class ThemeTestScreen extends StatelessWidget {
  const ThemeTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colors = ThemeHelper.getColorsWithListener(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      appBar: AppBar(
        title: const Text('Test du Thème'),
        backgroundColor: colors.textAccent,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test des styles de texte
            Text(
              'Titre Principal',
              style: ThemeHelper.getTitleStyle(context),
            ),
            const SizedBox(height: 16),
            Text(
              'Sous-titre avec style adaptatif',
              style: ThemeHelper.getSubtitleStyle(context),
            ),
            const SizedBox(height: 24),
            
            // Test de la carte
            Card(
              color: colors.card,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Carte avec thème adaptatif',
                      style: ThemeHelper.getSectionTitleStyle(context),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Cette carte change de couleur selon le thème sélectionné.',
                      style: ThemeHelper.getSubtitleStyle(context),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Test du champ de saisie
            TextFormField(
              decoration: ThemeHelper.getInputDecoration(
                context,
                labelText: 'Champ de test',
                hintText: 'Tapez quelque chose...',
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Test des boutons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    style: ThemeHelper.getPrimaryButtonStyle(context),
                    onPressed: () {},
                    child: Text(
                      'Bouton Principal',
                      style: ThemeHelper.getButtonTextStyle(context),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    style: ThemeHelper.getSecondaryButtonStyle(context),
                    onPressed: () {},
                    child: Text(
                      'Bouton Secondaire',
                      style: ThemeHelper.getButtonTextStyle(context),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Contrôles de thème
            Text(
              'Contrôles du thème :',
              style: ThemeHelper.getSectionTitleStyle(context),
            ),
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () => themeProvider.setThemeMode(AppThemeMode.light),
                  child: const Text('Clair'),
                ),
                ElevatedButton(
                  onPressed: () => themeProvider.setThemeMode(AppThemeMode.dark),
                  child: const Text('Sombre'),
                ),
                ElevatedButton(
                  onPressed: () => themeProvider.setThemeMode(AppThemeMode.system),
                  child: const Text('Système'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Indicateur du mode actuel
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colors.backgroundSecondary,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colors.borderPrimary),
              ),
              child: Row(
                children: [
                  Icon(
                    themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                    color: colors.textAccent,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Mode actuel : ${themeProvider.isDarkMode ? "Sombre" : "Clair"}',
                    style: ThemeHelper.getTextStyle(context, fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
