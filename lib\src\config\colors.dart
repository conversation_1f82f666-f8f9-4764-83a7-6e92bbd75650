import 'package:flutter/material.dart';

class AppColors {
  // Primary Brand Colors
  static const Color primaryOrange = Color(0xFFFF5722); // Main brand color
  static const Color pureBlack = Color(0xFF000000);
  static const Color pureWhite = Color(0xFFFFFFFF);

  // Orange Variations
  static const Color orangeLight = Color(0xFFFF8A65); // Lighter shade
  static const Color orangeDark = Color(0xFFE64A19);  // Darker shade
  static const Color orangePale = Color(0xFFFFEBE7);  // Very light orange for backgrounds

  // Neutral Colors
  static const Color darkGray = Color(0xFF212121);    // For text
  static const Color mediumGray = Color(0xFF757575);  // For secondary text
  static const Color lightGray = Color(0xFFF5F5F5);   // For backgrounds
  static const Color borderGray = Color(0xFFE0E0E0);  // For borders

  // Status Colors
  static const Color success = Color(0xFF4CAF50);     // Green for success
  static const Color error = Color(0xFFD32F2F);       // Red for errors
  static const Color warning = Color(0xFFFFA726);     // Orange for warnings
  static const Color info = Color(0xFF2196F3);        // Blue for info

  // Background Colors
  static const Color backgroundPrimary = pureWhite;
  static const Color backgroundSecondary = lightGray;
  static const Color backgroundDark = pureBlack;
  static const Color backgroundAccent = orangePale;

  // Text Colors
  static const Color textPrimary = pureBlack;
  static const Color textSecondary = darkGray;
  static const Color textLight = pureWhite;
  static const Color textAccent = primaryOrange;

  // Button Colors
  static const Color buttonPrimary = primaryOrange;
  static const Color buttonSecondary = pureBlack;
  static const Color buttonDisabled = mediumGray;

  // Border Colors
  static const Color borderPrimary = borderGray;
  static const Color borderFocused = primaryOrange;
  static const Color borderError = error;

  // Shadow Colors
  static Color shadowColor = pureBlack.withOpacity(0.1);
  static Color overlayColor = pureBlack.withOpacity(0.5);
}
