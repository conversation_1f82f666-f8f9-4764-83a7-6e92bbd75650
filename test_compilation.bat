@echo off
echo Testing Flutter compilation...
echo.

echo Step 1: Cleaning project...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed
    pause
    exit /b 1
)

echo.
echo Step 2: Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo Step 3: Analyzing code...
flutter analyze
if %errorlevel% neq 0 (
    echo WARNING: Flutter analyze found issues
    echo This is normal for some warnings, continuing...
)

echo.
echo Step 4: Testing compilation (dry run)...
flutter build apk --debug --dry-run
if %errorlevel% neq 0 (
    echo ERROR: Flutter build failed
    pause
    exit /b 1
)

echo.
echo SUCCESS: All tests passed!
echo The AndroidManifest.xml issue has been resolved.
echo You can now run: flutter run
echo.
pause
