import 'package:droit/src/widgets/settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:flutter_langdetect/flutter_langdetect.dart' as langdetect;
import 'src/widgets/locale_provider.dart';
import 'src/widgets/signin.dart';
import 'src/config/theme_provider.dart';
import 'src/config/app_themes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await langdetect.initLangDetect();
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LocaleProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return MaterialApp(
      title: 'Permission de Construire',
      debugShowCheckedModeBanner: false,
      locale: localeProvider.locale,
      theme: AppThemes.lightTheme,
      darkTheme: AppThemes.darkTheme,
      themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', 'SA'),
        Locale('fr', ''),
      ],
      routes: {
        '/': (context) => const SignInScreen(),
        '/settings': (context) => const SettingsScreen(),
      },
      builder: (context, child) {
        // Update theme provider with system brightness changes
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final brightness = MediaQuery.of(context).platformBrightness;
          themeProvider.updateSystemBrightness(brightness);
        });

        return Directionality(
          textDirection: localeProvider.locale.languageCode == 'ar'
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: child!,
        );
      },
      initialRoute: '/',
    );
  }
}
