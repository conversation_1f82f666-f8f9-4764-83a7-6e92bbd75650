import 'package:droit/src/widgets/settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:flutter_langdetect/flutter_langdetect.dart' as langdetect;
import 'src/widgets/locale_provider.dart';
import 'src/widgets/signin.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await langdetect.initLangDetect();
  runApp(
    ChangeNotifierProvider(
      create: (_) => LocaleProvider(),
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);

    return MaterialApp(
      title: 'Permission de Construire',
      debugShowCheckedModeBanner: false,
      locale: localeProvider.locale,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', 'SA'),
        Locale('fr', ''),
      ],
      routes: {
        '/': (context) => const SignInScreen(),
        '/settings': (context) => const SettingsScreen(),
      },
      builder: (context, child) {
        return Directionality(
          textDirection: localeProvider.locale.languageCode == 'ar'
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: child!,
        );
      },
      initialRoute: '/',
      // Remove the key property to prevent full app rebuild on locale change
    );
  }
}
