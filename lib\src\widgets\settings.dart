
import 'package:droit/src/widgets/base_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../widgets/locale_provider.dart';
import '../widgets/signin.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';

    return BaseScreen(
      currentIndex: 1,
      child: Padding(
        padding: Constants.screenPadding,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isArabic ? 'الإعدادات' : 'Paramètres',
                style: Constants.titleStyle,
                textAlign: isArabic ? TextAlign.right : TextAlign.left,
              ),
              const SizedBox(height: Constants.largeSpacing),
              _buildProfileSection(isArabic, context),
              const SizedBox(height: Constants.extraLargeSpacing),
              _buildLanguageSection(isArabic, localeProvider, context),
              const SizedBox(height: Constants.extraLargeSpacing),
              _buildLogoutButton(isArabic, context),
            ],
          ),
        ),
      ),
    );
  }
 Widget _buildProfileSection(bool isArabic, BuildContext context) {
    return Card(
      elevation: Constants.cardTheme.elevation,
      shape: Constants.cardTheme.shape,
      color: Constants.cardTheme.color,
      child: Padding(
        padding: Constants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'الملف الشخصي' : 'Profil',
              style: Constants.sectionTitleStyle,
              textAlign: isArabic ? TextAlign.right : TextAlign.left,
            ),
            const SizedBox(height: Constants.mediumSpacing),
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: AppColors.primaryOrange,
                child: Icon(Icons.person, color: AppColors.pureWhite),
              ),
              title: Text(
                isArabic ? 'محمد أحمد' : 'Jean Dupont',
                style: Constants.subtitleStyle,
              ),
              subtitle: Text(
                '<EMAIL>',
                style: Constants.subtitleStyle,
              ),
            ),
            const SizedBox(height: Constants.mediumSpacing),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) => const EditProfileBottomSheet(),
  );
},

                style: Constants.primaryButtonStyle,
                child: Text(
                  isArabic ? 'تعديل الملف الشخصي' : 'Modifier le profil',
                  style: Constants.buttonTextStyle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildLanguageSection(bool isArabic, LocaleProvider localeProvider, BuildContext context) {
  return Card(
    elevation: Constants.cardTheme.elevation,
    shape: Constants.cardTheme.shape,
    color: Constants.cardTheme.color,
    child: Padding(
      padding: Constants.cardPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'اللغة' : 'Langue',
            style: Constants.sectionTitleStyle,
            textAlign: isArabic ? TextAlign.right : TextAlign.left,
          ),
          const SizedBox(height: Constants.mediumSpacing),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'اختر اللغة' : 'Choisir la langue',
                style: Constants.subtitleStyle,
              ),
              DropdownButton<String>(
                value: isArabic ? 'العربية' : 'Français',
                items: const [
                  DropdownMenuItem(
                    value: 'العربية',
                    child: Text('العربية'),
                  ),
                  DropdownMenuItem(
                    value: 'Français',
                    child: Text('Français'),
                  ),
                ],
                onChanged: (value) {
                  if (value == 'Français') {
                    localeProvider.setLocale(const Locale('fr'));
                  } else if (value == 'العربية') {
                    localeProvider.setLocale(const Locale('ar', 'SA'));
                  }
                },
                style: Constants.subtitleStyle.copyWith(color: AppColors.primaryOrange),
                dropdownColor: AppColors.pureWhite,
                iconEnabledColor: AppColors.primaryOrange,
                underline: Container(
                  height: 2,
                  color: AppColors.primaryOrange,
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}

 

  Widget _buildLogoutButton(bool isArabic, BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          // Déconnexion avec nettoyage et redirection vers SignInScreen
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const SignInScreen()),
            (route) => false,
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
        ),
        child: Text(
          isArabic ? 'تسجيل الخروج' : 'Déconnexion',
          style: Constants.buttonTextStyle.copyWith(color: AppColors.pureWhite),
        ),
      ),
    );
  }
}
class EditProfileBottomSheet extends StatefulWidget {
  const EditProfileBottomSheet({super.key});

  @override
  State<EditProfileBottomSheet> createState() => _EditProfileBottomSheetState();
}

class _EditProfileBottomSheetState extends State<EditProfileBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  LocaleProvider? localeProvider;

  @override
  void initState() {
    super.initState();
    _nameController.text = 'Jean Dupont';
    _emailController.text = '<EMAIL>';
    _phoneController.text = '+212 6XX XXX XXX';
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    localeProvider = Provider.of<LocaleProvider>(context, listen: false);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required bool isArabic,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool obscureText = false,
    bool? obscureState,
    VoidCallback? onToggleObscure,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureState ?? obscureText,
      keyboardType: keyboardType,
      textAlign: isArabic ? TextAlign.right : TextAlign.left,
      decoration: InputDecoration(
        labelText: label,
        border: Constants.defaultBorder,
        focusedBorder: Constants.focusedBorder,
        suffixIcon: obscureText && onToggleObscure != null
            ? IconButton(
                icon: Icon(
                  obscureState! ? Icons.visibility_off : Icons.visibility,
                  color: AppColors.primaryOrange,
                ),
                onPressed: onToggleObscure,
              )
            : null,
      ),
      validator: validator,
    );
  }

  Future<void> _handleSubmit() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      
      try {
        // Simuler une requête API
        await Future.delayed(const Duration(seconds: 1));
        
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                localeProvider?.locale.languageCode == 'ar'
                    ? 'تم تحديث الملف الشخصي بنجاح'
                    : 'Profil mis à jour avec succès'
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                localeProvider?.locale.languageCode == 'ar'
                    ? 'حدث خطأ أثناء تحديث الملف الشخصي'
                    : 'Erreur lors de la mise à jour du profil'
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Provider.of<LocaleProvider>(context).locale.languageCode == 'ar';

    return Container(
      padding: EdgeInsets.only(
        top: 20,
        left: 20,
        right: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    isArabic ? 'تعديل الملف الشخصي' : 'Modifier le profil',
                    style: Constants.sectionTitleStyle,
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                controller: _nameController,
                label: isArabic ? 'الاسم الكامل' : 'Nom complet',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return isArabic ? 'الرجاء إدخال الاسم' : 'Veuillez entrer votre nom';
                  }
                  if (value.length < 3) {
                    return isArabic ? 'الاسم قصير<|im_start|>' : 'Nom trop court';
                  }
                  return null;
                },
                isArabic: isArabic,
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                controller: _emailController,
                label: isArabic ? 'البريد الإلكتروني' : 'Email',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return isArabic ? 'الرجاء إدخال البريد الإلكتروني' : 'Veuillez entrer votre email';
                  }
                  final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
                  if (!emailRegex.hasMatch(value)) {
                    return isArabic ? 'البريد الإلكتروني غير صالح' : 'Email invalide';
                  }
                  return null;
                },
                isArabic: isArabic,
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                controller: _phoneController,
                label: isArabic ? 'رقم الهاتف' : 'Téléphone',
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final phoneRegex = RegExp(r'^\+?\d{8,}$');
                    if (!phoneRegex.hasMatch(value.replaceAll(RegExp(r'\s+'), ''))) {
                      return isArabic ? 'رقم الهاتف غير صالح' : 'Numéro de téléphone invalide';
                    }
                  }
                  return null;
                },
                isArabic: isArabic,
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                controller: _passwordController,
                label: isArabic ? 'كلمة المرور الجديدة' : 'Nouveau mot de passe',
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (value.length < 8) {
                      return isArabic 
                          ? 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
                          : 'Le mot de passe doit contenir au moins 8 caractères';
                    }
                  }
                  return null;
                },
                isArabic: isArabic,
                obscureText: true,
                obscureState: _obscurePassword,
                onToggleObscure: () => setState(() => _obscurePassword = !_obscurePassword),
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                controller: _confirmPasswordController,
                label: isArabic ? 'تأكيد كلمة المرور' : 'Confirmer le mot de passe',
                validator: (value) {
                  if (_passwordController.text.isNotEmpty) {
                    if (value != _passwordController.text) {
                      return isArabic
                          ? 'كلمات المرور غير متطابقة'
                          : 'Les mots de passe ne correspondent pas';
                    }
                  }
                  return null;
                },
                isArabic: isArabic,
                obscureText: true,
                obscureState: _obscureConfirmPassword,
                onToggleObscure: () => setState(() => _obscureConfirmPassword = !_obscureConfirmPassword),
              ),
              const SizedBox(height: Constants.largeSpacing),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _handleSubmit,
                  style: Constants.primaryButtonStyle,
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          isArabic ? 'حفظ التغييرات' : 'Enregistrer',
                          style: Constants.buttonTextStyle,
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
