@echo off
echo Fixing Flutter dependencies and namespace issues...
echo.

echo Step 1: Cleaning project completely...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed
    pause
    exit /b 1
)

echo.
echo Step 2: Removing pubspec.lock and build folders...
if exist pubspec.lock del pubspec.lock
if exist build rmdir /s /q build
if exist .dart_tool rmdir /s /q .dart_tool

echo.
echo Step 3: Getting minimal dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo Step 4: Testing basic compilation...
flutter analyze --no-fatal-infos
if %errorlevel% neq 0 (
    echo WARNING: Flutter analyze found issues, but continuing...
)

echo.
echo Step 5: Testing build (dry run)...
flutter build apk --debug --dry-run
if %errorlevel% neq 0 (
    echo ERROR: Flutter build failed
    echo.
    echo Possible solutions:
    echo 1. Check Android SDK installation
    echo 2. Update Flutter: flutter upgrade
    echo 3. Check Java version: java -version
    echo 4. Clean Android cache: flutter clean
    pause
    exit /b 1
)

echo.
echo SUCCESS: Dependencies fixed and build works!
echo.
echo The following packages were removed to fix compatibility:
echo - flutter_langdetect (language detection)
echo - file_picker (file selection)
echo - youtube_player_flutter (video player)
echo.
echo You can now run: flutter run
echo.
echo To add back file picker functionality later, use:
echo flutter pub add file_picker
echo.
pause
