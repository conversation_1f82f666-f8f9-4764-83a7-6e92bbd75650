import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../widgets/locale_provider.dart';
import '../widgets/dashboard.dart';
import '../widgets/signup.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى إدخال البريد الإلكتروني' : 'Veuillez entrer votre email';
    }
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(value)) {
      return isArabic ? 'البريد الإلكتروني غير صالح' : 'Email invalide';
    }
    return null;
  }

  String? _validatePassword(String? value, bool isArabic) {
    if (value == null || value.isEmpty) {
      return isArabic ? 'يرجى إدخال كلمة المرور' : 'Veuillez entrer votre mot de passe';
    }
    if (value.length < 8) {
      return isArabic ? 'كلمة المرور قصيرة' : 'Le mot de passe est trop court';
    }
    return null;
  }

  void _handleSignIn(BuildContext context) {
    if (_formKey.currentState!.validate()) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const DashboardScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';

    return Scaffold(
      backgroundColor: AppColors.lightGray, // #E5E7EB
      body: Padding(
        padding: Constants.screenPadding,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildPlatformHeader(isArabic),
                const SizedBox(height: Constants.extraLargeSpacing),
                _buildLoginCard(isArabic),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlatformHeader(bool isArabic) => Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            isArabic ? 'منصة بناء' : 'Plateforme Bâtir',
            style: Constants.titleStyle, // Uses primaryOrange (#FF6200)
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: Constants.smallSpacing),
          Text(
            isArabic ? 'اهلا بك في منصة بناء' : 'Bienvenue sur la plateforme Bâtir',
            style: Constants.subtitleStyle, // Uses lightGray (#E5E7EB)
            textAlign: TextAlign.center,
          ),
        ],
      );

  Widget _buildLoginCard(bool isArabic) => Card(
        elevation: Constants.cardTheme.elevation,
        shape: Constants.cardTheme.shape,
        color: Constants.cardTheme.color, // Uses pureWhite (#FFFFFF)
        child: Padding(
          padding: Constants.cardPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildLoginHeader(isArabic),
              const SizedBox(height: Constants.largeSpacing),
              _buildEmailField(isArabic),
              const SizedBox(height: Constants.mediumSpacing),
              _buildPasswordField(isArabic),
              const SizedBox(height: Constants.largeSpacing),
              _buildLoginButton(isArabic),
              const SizedBox(height: Constants.mediumSpacing),
              _buildFooterLinks(isArabic),
            ],
          ),
        ),
      );

  Widget _buildLoginHeader(bool isArabic) => Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            isArabic ? 'تسجيل الدخول' : 'Connexion',
            style: Constants.sectionTitleStyle, // Uses primaryOrange (#FF6200)
            textAlign: isArabic ? TextAlign.right : TextAlign.left,
          ),
          const SizedBox(height: Constants.smallSpacing),
          Text(
            isArabic
                ? 'أدخل بريدك الإلكتروني وكلمة المرور للوصول إلى حسابك'
                : 'Entrez votre email et mot de passe pour accéder à votre compte',
            style: Constants.subtitleStyle, // Uses lightGray (#E5E7EB)
            textAlign: isArabic ? TextAlign.right : TextAlign.left,
          ),
        ],
      );

  Widget _buildEmailField(bool isArabic) => TextFormField(
        controller: _emailController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateEmail(value, isArabic),
        keyboardType: TextInputType.emailAddress,
        decoration: InputDecoration(
          labelText: isArabic ? 'البريد الإلكتروني *' : 'Email *',
          hintText: '<EMAIL>',
          alignLabelWithHint: true,
          floatingLabelAlignment:
              isArabic ? FloatingLabelAlignment.start : FloatingLabelAlignment.start,
          hintStyle: const TextStyle(color: AppColors.lightGray), // #E5E7EB
          border: Constants.defaultBorder, // Uses lightGray (#E5E7EB)
          focusedBorder: Constants.focusedBorder, // Uses primaryOrange (#FF6200)
        ),
      );

  Widget _buildPasswordField(bool isArabic) => TextFormField(
        controller: _passwordController,
        obscureText: true,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validatePassword(value, isArabic),
        decoration: InputDecoration(
          labelText: isArabic ? 'كلمة المرور *' : 'Mot de passe *',
          hintText: '********',
          alignLabelWithHint: true,
          floatingLabelAlignment:
              isArabic ? FloatingLabelAlignment.start : FloatingLabelAlignment.start,
          hintStyle: const TextStyle(color: AppColors.lightGray), // #E5E7EB
          border: Constants.defaultBorder, // Uses lightGray (#E5E7EB)
          focusedBorder: Constants.focusedBorder, // Uses primaryOrange (#FF6200)
        ),
      );

  Widget _buildLoginButton(bool isArabic) => SizedBox(
        width: double.infinity,
        child: Builder(
          builder: (context) => ElevatedButton(
            onPressed: () => _handleSignIn(context),
            style: Constants.primaryButtonStyle, // Uses primaryOrange (#FF6200) and pureWhite (#FFFFFF)
            child: Text(
              isArabic ? 'تسجيل الدخول' : 'Connexion',
              style: Constants.buttonTextStyle, // Uses pureWhite (#FFFFFF)
            ),
          ),
        ),
      );

  Widget _buildFooterLinks(bool isArabic) => Column(
        children: [
          Align(
            alignment: Alignment.center,
            child: Builder(
              builder: (context) => Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    isArabic ? 'ليس لديك حساب؟' : 'Pas de compte ?',
                    style: Constants.subtitleStyle, // Uses lightGray (#E5E7EB)
                  ),
                  TextButton(
                    onPressed: () => Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const SignUpScreen()),
                    ),
                    child: Text(
                      isArabic ? 'إنشاء حساب' : 'Créer un compte',
                      style: Constants.subtitleStyle.copyWith(color: AppColors.primaryOrange),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
}
